<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{ce9fc52f-b1bc-d313-fb79-5dd1af79a2af}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp-Editor</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE;UNITY_2020_3_47;UNITY_2020_3;UNITY_2020;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_MONO_BDWGC;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;PLATFORM_STANDALONE;PLATFORM_STANDALONE_WIN;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;ENABLE_WEBSOCKET_HOST;ENABLE_MONO;NET_4_6;ENABLE_PROFILER;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;AMPLIFY_SHADER_EDITOR;DISABLE_AIHELP;UNITY_POST_PROCESSING_STACK_V2;ODIN_INSPECTOR;ODIN_INSPECTOR_3;FISH_PORTRAIT;FISH_DEBUG;FISH_PAY;PLAYMAKER_UTILS;PLAYMAKER;PLAYMAKER_1_9;PLAYMAKER_1_9_1;PLAYMAKER_1_8_OR_NEWER;PLAYMAKER_1_8_5_OR_NEWER;PLAYMAKER_1_9_OR_NEWER;PLAYMAKER_TMPRO;USE_HUATUO;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER;NET_STANDARD_2_0</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Tools\AnimationTools.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Math\Editor\Vector2RandomValueEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\SetAnimatorIntActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\UnLoadSceneAsynchCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Editor\FsmTemplate.cs" />
     <Compile Include="Assets\PlayMaker\Editor\FsmStateWindow.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerEvent\Editor\PlayMakerEventPropertyDrawer.cs" />
     <Compile Include="Assets\PlayMaker\Actions\StateMachine\Editor\BlockEventsEditor.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerGUIInspector.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\ShaderPreprocessor.cs" />
     <Compile Include="Assets\GameScript\Editor\International\ImportTool.cs" />
     <Compile Include="Assets\GameScript\Editor\ShaderGUI\CustomShaderGUI_Base.cs" />
     <Compile Include="Assets\GameScript\Editor\Artist\ParticleSystemScan.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Profiler\AnalyzeParticleProfiler.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Quaternion\Editor\GetQuaternionEulerAnglesCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorDeltaActionEditor.cs" />
     <Compile Include="Assets\ThirdPart\Unity-Logs-Viewer\Reporter\Editor\ReporterEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\EnumCreator\Editor\EnumCreatorWizard.cs" />
     <Compile Include="Assets\GameScript\Editor\GM\GMTools.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Editor\SetPropertyEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\PrefabTextBrowser.cs" />
     <Compile Include="Assets\GameScript\Editor\Analyze\AssetDisplay.cs" />
     <Compile Include="Assets\PlayMaker\Editor\FsmActionWindow.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Tween\Editor\TweenFadeEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\FindSkinAsset.cs" />
     <Compile Include="Assets\GameScript\Editor\ABBuilder\ABBuilderPackage.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\Internal\GetSceneActionBaseCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\Owner\Editor\OwnerPropertyDrawer.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Tween\Editor\TweenVariableEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorAnimatorRootActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\FindMissing\FindMissingWindow.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Schema\AssetPrefabSchema.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\EntitlementsPostProcess.cs" />
     <Compile Include="Assets\GameScript\Editor\International\Transplantor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\ReorderableListStyles.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorPivotActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerControlsEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\Analyze\AnalyseAllInuseAssets.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\LoadPrefabActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\GetInnerPropertyActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Editor\FsmTemplateWindow.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerAutoUpdater.cs" />
     <Compile Include="Assets\GameScript\Editor\FishExporter\FishExporter.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\MergeScenesCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerGlobalsInspector.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Editor\CallMethodEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorTargetActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Tween\Editor\TweenPunchEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Tools\RegularTools.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\EditorPlayPrepare.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\ReorderableListControl.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerFsmVariableTarget\Editor\PlayMakerFsmVariableTargetPropertyDrawer.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerCurrentEventDataEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Editor\LookAtActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\International\ImportFntTool.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Physics2D\Editor\SmoothLookAt2dEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBuildPrepareTools\TaskHuatuoDisable.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionLowPassFilterCustomEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\ILRuntime\ILRuntimeAssemblyBuilder.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\GetSceneRootGameObjectsCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\ElementAdderMeta.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionCustomEditorBase.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Physics\Editor\RaycastAllEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\ParseJsonActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\BuildToolsHW.cs" />
     <Compile Include="Assets\GameScript\Editor\ILRuntimeBuildManager.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Schema\BasePackageSchema.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerUtilsDefine.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Tools\ModelTools.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Tween\Editor\TweenUiPositionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Schema\AssetBitmapSchema.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\GenericElementAdderMenu.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Tween\Editor\TweenScaleEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\BundleBuildNewHW.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\EnumCreator\Editor\EnumFileFinder.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerUpgradeTools.cs" />
     <Compile Include="Assets\PlayMaker\Editor\DefinesHelper.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Editor\PlayMakerEventProxyEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Schema\AssetFBXSchema.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\ElementAdderMenuCommandAttribute.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\IReorderableListDropTarget.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Components\Editor\CommentInspector.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\GetSceneIsLoadedCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Debug\Editor\CommentEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\AIHelpBuildPostProcessor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Tween\Editor\TweenColorEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\ParticleProfiler\ParticleProfilerWindow.cs" />
     <Compile Include="Assets\GameScript\Editor\ParticleFixer\ParticleFixer.cs" />
     <Compile Include="Assets\GameScript\Editor\Analyze\AnalyseUtils.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\IElementAdderMenuBuilder.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\BundleReport.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\ButtonAttribute\Editor\ButtonDrawer.cs" />
     <Compile Include="Assets\GameScript\Editor\ShaderGUI\SEFullEffectInShowShaderGUI.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerUpdater.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionLookRotationCustomEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\Artist\RemoveMissingScripts.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBuildPrepareTools\TaskCreatePrefabManifest.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerAddonManager.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\IElementAdderMenu.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Editor\RunFsmEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBuildPrepareTools\TaskReimportCollectXML.cs" />
     <Compile Include="Assets\GameScript\Editor\Artist\ArtistTools.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\SpriteReferenceChecker.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerUpgradeGuide.cs" />
     <Compile Include="Assets\GameScript\Editor\ParticleProfiler\ParticleProfiler.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\IElementAdderMenuCommand.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\RuleExtension\ActiveRuleExtension.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\BundleBuild.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Editor\SetCameraFOVActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Tween\Editor\TweenPositionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\ReorderableListFlags.cs" />
     <Compile Include="Assets\PlayMaker\Editor\FsmComponentInspector.cs" />
     <Compile Include="Assets\GameScript\Editor\ExportDependencies.cs" />
     <Compile Include="Assets\GameScript\Editor\YooScanner\Schema\AssetFBXSchema.cs" />
     <Compile Include="Assets\GameScript\Editor\Analyze\AnalyseFinder.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\BuildPostProcessorHW.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\LoadSceneAsynchCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\Editor\PlayMakerPropertyDrawerBaseClass.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBuildPrepareTools\PrefabManifestPrepareTools.cs" />
     <Compile Include="Assets\PlayMaker\Editor\ContextToolWindow.cs" />
     <Compile Include="Assets\[废弃]\Tools\Editor\MaterialPreviewEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Internal\SerializedPropertyUtility.cs" />
     <Compile Include="Assets\GameScript\Editor\Analyze\AnalyseILRBehaviour.cs" />
     <Compile Include="Assets\GameScript\Editor\ShaderGUI\CustomStandardGUIV2.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Editor\MoveTowardsActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Schema\AssetMaterialSchema.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\GetSceneNameCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Tween\Editor\TweenUiSizeEditor.cs" />
     <Compile Include="Assets\PlayMaker\Editor\BaseGuidedTourWindow.cs" />
     <Compile Include="Assets\GameScript\Editor\PatchDecrypter\PatchDecrypterWindow.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Schema\ReferenceSchema.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorCurrentTransitionInfoActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\UICustomCreator.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionSlerpCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerEditorUtils.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker uGui\Editor\PlayMakerUGuiComponentProxyInspector.cs" />
     <Compile Include="Assets\GameScript\Editor\Utility\FileUtility.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Tween\Editor\TweenCameraEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\PrefabEffectReplaceTool.cs" />
     <Compile Include="Assets\GameScript\Editor\ShaderGUI\PropertyDrawer.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorGravityWeightActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\Trails\Editor\TrailEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionEulerCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker uGui\Editor\PlayMakerUGuiCanvasRaycastFilterEventsProxyInspector.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\GActionFsmEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\GetHotBehaviourActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\ClientLanguageExpand.cs" />
     <Compile Include="Assets\[废弃]\Tools\RenameTool\Editor\RBPackageExporter.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBuildPrepareTools\TaskCopyBuildFile.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker uGui\Editor\PlayMakerUGuiDropEventsProxyInspector.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Tween\Editor\TweenPropertyEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Tween\Editor\TweenEditorBase.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerMainMenu.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerDefines.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorCurrentStateInfoIsNameActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerInspectorUtils.cs" />
     <Compile Include="Assets\GameScript\Editor\YooScanner\Schema\DependencySchema.cs" />
     <Compile Include="Assets\GameScript\Editor\ShaderGUI\CustomStandardGUI.cs" />
     <Compile Include="Assets\[废弃]\Tools\RenameTool\Editor\RBPackageExporterCLI.cs" />
     <Compile Include="Assets\GameScript\Editor\International\TransplantWindow.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Tween\Editor\TweenQuaternionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBuildPrepareTools\AssetBuildPrepareTools.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\BuildPostProcessor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\MoveGameObjectToSceneCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\GetSceneBuildIndexCustomEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\Analyze\AnalyseMoveUtils.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\MaterialSearchWindow.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\WingAndCannonShowTool.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorCurrentStateInfoActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\Analyze\AnalyseReferences.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\BundleBuildNew.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\IReorderableListAdaptor.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBuildPrepareTools\TaskBindingILRuntime.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\SetAnimatorFloatActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorBoneGameObjectActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerInspectorUtils_Events.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\GetCLRPropertyActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Editor\FsmTimelineWindow.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\SetAnimatorBoolActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Tools\PrefabTools.cs" />
     <Compile Include="Assets\[废弃]\Tools\Editor\ResetMaterialTool.cs" />
     <Compile Include="Assets\GameScript\Editor\Trails\Editor\TrailPreviewUtillity.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionLerpCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Editor\FsmGlobalsWindow.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Internal\GUIHelper.cs" />
     <Compile Include="Assets\GameScript\Editor\Artist\SVNTools.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\LinkerWizard\Editor\LinkerEditorChecks.cs" />
     <Compile Include="Assets\GameScript\Editor\Analyze\Analyse.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Quaternion\Editor\GetQuaternionMultipliedByQuaternionCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Tween\Editor\TweenRotationEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\ShaderGUI\CustomStandardGUIV3.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\MatRefrenceChecker.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\PlayMakerEventProxy\Editor\PlayMakerEventProxyCreator.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\AssetsNameStandardization.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\GenericListAdaptor.cs" />
     <Compile Include="Assets\GameScript\Editor\ShaderGUI\CustomShaderGUI.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\SetCLRPropertyActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\GetScenePathCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\ReorderableListEvents.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\GetSceneIsValidCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorFloatActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\ClearPlayerSetting.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorNextStateInfoActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\Analyze\AnalyseSprite.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Tools\ShaderTools.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorSpeedActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorLayerWeightActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Profiler\AnalyzeTextureProfiler.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\RuleExtension\GroupRuleExtension.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerGuidedTour.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\BossCoinExplodeNodeSetTool.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PreUpdateChecker.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorCurrentTransitionInfoIsUserNameActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\Analyze\AnalyseAllUnuseAssets.cs" />
     <Compile Include="Assets\PlayMaker\Editor\EditorStartupPrefs.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBuildPrepareTools\TaskCheckBuildTxt.cs" />
     <Compile Include="Assets\GameScript\Editor\ShaderGUI\FishShaderGUI.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\MainCameraTarget\Editor\MainCameraTargetPropertyDrawer.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Tools\MaterialTools.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\CallInnerMethodActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Editor\AboutWindow.cs" />
     <Compile Include="Assets\GameScript\Editor\YooScanner\Schema\AssetMaterialSchema.cs" />
     <Compile Include="Assets\PlayMaker\Editor\FsmEditorWindow.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerBuildCallbacks.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\PlayMakerEventProxy\Editor\PlayMakerEventProxyCreatorWizard.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\AutoSetTrackFile.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\RuleExtension\PackRuleExtension.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\PlayMakerEventProxy\Editor\EventProxyFileFinder.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\DataStoreInspector.cs" />
     <Compile Include="Assets\GameScript\Editor\ILRuntime\ILRuntimeInitialize.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker uGui\Editor\PlayMakerUGuiPointerEventsProxyInspector.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\CallCLRMethodActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\International\TranslationTableExpand.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerEventTarget\Editor\PlayMakerEventTargetPropertyDrawer.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\SetActiveSceneCustomEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\Artist\ReplayJason.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\BundleEncrypter.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\GetHotBehaviourInChildrenActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\Trails\Editor\TrailEditor_Base.cs" />
     <Compile Include="Assets\GameScript\Editor\UIAtlasProfiler\UIAtlasProfilerWindow.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Tools\SchemaTools.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerCustomActionWizard.cs" />
     <Compile Include="Assets\GameScript\Editor\Utility\AssemblyUtility.cs" />
     <Compile Include="Assets\GameScript\Editor\ABBuilder\ABBuilderServer.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\MergePrefab.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Quaternion\Editor\GetQuaternionMultipliedByVectorCustomEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\HybridCLR\CLRTools.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\ClientConfigExpand.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Schema\DependencySchema.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\IElementAdder.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\EnumCreator\Editor\EnumCreator.cs" />
     <Compile Include="Assets\GameScript\Editor\Analyze\AnalyseBundle.cs" />
     <Compile Include="Assets\PlayMaker\Editor\FsmTemplateEditor.cs" />
     <Compile Include="Assets\[废弃]\Tools\RenameTool\Editor\RBPackageExporterWindow.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerFsmVariable\Editor\PlayMakerFsmVariablePropertyDrawer.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionRotateTowardsCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorIntActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerWelcomeWindow.cs" />
     <Compile Include="Assets\PlayMaker\Editor\AssetGUIDs.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\GetSceneRootCountCustomEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\International\MultiLanguage.cs" />
     <Compile Include="Assets\GameScript\Editor\FinderTools\FindMaterialsWithShader.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBuildPrepareTools\TaskCheckPanelFolderName.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionAngleAxisCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerStats.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\PrefabTool2D.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\RuleExtension\FilterRuleExtension.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionInverseCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\ReorderableListGUI.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\BuildTools.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBundle\AssetDependencyCache.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorIKGoalActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Physics\Editor\FindOverlapsEditor.cs" />
     <Compile Include="Assets\PlayMaker\Editor\FsmSelectorWindow.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerBugReportWindow.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerInspectorUtils_Reflection.cs" />
     <Compile Include="Assets\PlayMaker\Editor\ReportWindow.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorIsMatchingTargetActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorBodyActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\ParseJsonValueCountActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\ElementAdderMenuBuilder.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\SerializedPropertyAdaptor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\UnLoadSceneCustomEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\YooScanner\Schema\AssetPrefabSchema.cs" />
     <Compile Include="Assets\GameScript\Editor\TextureAssetsManager\TextureNameChecker.cs" />
     <Compile Include="Assets\GameScript\Editor\ShaderGUI\CustomShaderGUI_Test.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Tools\TextureTools.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\GetSceneIsDirtyCustomEditor.cs" />
     <Compile Include="Assets\[废弃]\Tools\Editor\MaterialCleaner.cs" />
     <Compile Include="Assets\GameScript\Editor\ILRuntime\ILRuntimeCLRBinding.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\BaseClasses\OnAnimatorUpdateActionEditorBase.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\LinkerWizard\Editor\LinkerDataCustomInspector.cs" />
     <Compile Include="Assets\GameScript\Editor\ShaderGUI\CustomSpriteGUI.cs" />
     <Compile Include="Assets\GameScript\Editor\ABBuilder\ABBuilder.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Editor\GetDistanceEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\CreateFnt.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\HierarchyPathContextMenu.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerProjectTools.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Internal\ReorderableListResources.cs" />
     <Compile Include="Assets\PlayMaker\Actions\UI\Editor\UiIcons.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\HdAssetsMappingTools.cs" />
     <Compile Include="Assets\GameScript\Editor\Utility\ApplicationMonitor.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\Sprites2Animation.cs" />
     <Compile Include="Assets\GameScript\Editor\Artist\TexturePathComparer.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\GetChildWithIndexActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Editor\FsmErrorWindow.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorCurrentTransitionInfoIsNameActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Physics2D\Editor\SetVelocity2dEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetArtScanner\Schema\AssetTextureSchema.cs" />
     <Compile Include="Assets\GameScript\Editor\PatchAnalyzer\PatchAnalyzerWindow.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorIsLayerInTransitionActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Quaternion\Editor\GetQuaternionFromRotationCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Actions\SceneManager\Editor\LoadSceneCustomEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker uGui\Editor\PlayMakerUGuiDragEventsProxyInspector.cs" />
     <Compile Include="Assets\PlayMaker\Editor\FsmEventsWindow.cs" />
     <Compile Include="Assets\GameScript\Editor\FindHotScript\FindHotScriptWindow.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorBoolActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerInspectorUtils_Variables.cs" />
     <Compile Include="Assets\PlayMaker\Editor\PlayMakerEditorStartup.cs" />
     <Compile Include="Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorCurrentStateInfoIsTagActionEditor.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerTimelineEventTarget\Editor\PlayMakerTimelineEventTargetPropertyDrawer.cs" />
     <Compile Include="Assets\GameScript\Editor\Tools\TextureSetting.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\GenericElementAdderMenuBuilder.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBuildPrepareTools\TaskMappingMajiaAssets.cs" />
     <Compile Include="Assets\GameScript\Editor\AssetBuildPrepareTools\TaskHuatuoEnable.cs" />
     <Compile Include="Assets\PlayMaker\Editor\FsmLogWindow.cs" />
     <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerFsmTarget\Editor\PlayMakerFsmTargetPropertyDrawer.cs" />
     <Compile Include="Assets\GameScript\Editor\ShaderGUI\TY_Effect_ParticleShaderGUI.cs" />
     <Compile Include="Assets\GameScript\Editor\YooScanner\Schema\AssetTextureSchema.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\CreateCLRInstanceActionEditor.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\PlayMakerEventListenHelperInspector.cs" />
     <Compile Include="Assets\GameScript\Editor\PlayMaker\PlayMakerNetworkListenHelperInspector.cs" />
     <None Include="Assets\PlayMaker\Editor\Install\Resources\CheckActions.txt" />
     <None Include="Assets\PlayMaker\Editor\Resources\CustomVariableTypes.txt" />
     <None Include="Assets\PlayMaker\Editor\Resources\VersionInfo.txt" />
     <None Include="Assets\PlayMaker\Editor\Install\readme.txt" />
     <None Include="Assets\UWA\UWA_GOT\Editor\Resources\serverip.txt" />
     <None Include="Assets\GameScript\Editor\EditorConig\ct_achievement_key.txt" />
    <Reference Include="UnityEngine">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsNativeModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsNativeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UNETModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PackageManagerUIModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.PackageManagerUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIServiceModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIServiceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\PlaybackEngines\AndroidPlayer\UnityEditor.Android.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="PlayMakerEditorResources">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\PlayMaker\Editor\PlayMakerEditorResources.dll</HintPath>
    </Reference>
    <Reference Include="ILRuntime.Mono.Cecil.Mdb">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\ILRuntime\Plugins\ILRuntime.Mono.Cecil.Mdb.dll</HintPath>
    </Reference>
    <Reference Include="DOTween">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\DOTween\DOTween.dll</HintPath>
    </Reference>
    <Reference Include="TexturePackerImporter">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\TexturePacker\Editor\TexturePackerImporter.dll</HintPath>
    </Reference>
    <Reference Include="LZ4">
        <HintPath>C:\Users\<USER>\Documents\game\client\Packages\com.code-philosophy.hybridclr\Plugins\LZ4.dll</HintPath>
    </Reference>
    <Reference Include="UWAShared">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\UWA\UWA_SDK\Runtime\ManagedLibs\UWAShared.dll</HintPath>
    </Reference>
    <Reference Include="proxima-websocket-sharp">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\ThirdPart\Proxima\WebSocketSharp\proxima-websocket-sharp.dll</HintPath>
    </Reference>
    <Reference Include="ConditionalExpressionEditor">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\PlayMaker\Editor\ConditionalExpressionEditor.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\ICSharpCode.SharpZipLib\netstandard2.0\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="ConsoleE">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\ConsoleE\Editor\ConsoleE.dll</HintPath>
    </Reference>
    <Reference Include="PlayMaker">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\PlayMaker\PlayMaker.dll</HintPath>
    </Reference>
    <Reference Include="PlayMakerEditor">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\PlayMaker\Editor\PlayMakerEditor.dll</HintPath>
    </Reference>
    <Reference Include="Ionic.Zlib">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\Ionic.Zlib.dll</HintPath>
    </Reference>
    <Reference Include="UWALib">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\UWA\UWA_GOT\Editor\UWALib.dll</HintPath>
    </Reference>
    <Reference Include="dnlib">
        <HintPath>C:\Users\<USER>\Documents\game\client\Packages\com.code-philosophy.hybridclr\Plugins\dnlib.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\JsonDotNet\Assemblies\Standalone\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="UWAEditor">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\UWA\UWA_SDK\Editor\UWAEditor.dll</HintPath>
    </Reference>
    <Reference Include="ILRuntime.Mono.Cecil.Pdb">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\ILRuntime\Plugins\ILRuntime.Mono.Cecil.Pdb.dll</HintPath>
    </Reference>
    <Reference Include="zxing.unity">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\zxing.unity.dll</HintPath>
    </Reference>
    <Reference Include="ConditionalExpression">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\PlayMaker\ConditionalExpression.dll</HintPath>
    </Reference>
    <Reference Include="ILRuntime.Mono.Cecil">
        <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\ILRuntime\Plugins\ILRuntime.Mono.Cecil.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\PackageCache\com.unity.ext.nunit@1.0.6\net35\unity-custom\nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Common.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Microsoft.CSharp.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Http.Rtc.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.Emit.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ServiceModel.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ServiceModel.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
        <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TestRunner">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\UnityEngine.TestRunner.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TestRunner">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\UnityEditor.TestRunner.dll</HintPath>
    </Reference>
    <Reference Include="HybridCLR.Runtime">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\HybridCLR.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VSCode.Editor">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.VSCode.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CacheServer">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\UnityEditor.CacheServer.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.Sprite.Editor">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.2D.Sprite.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UI">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
    </Reference>
    <Reference Include="HybridCLR.Editor">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\HybridCLR.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UI">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.DeviceSimulator.Editor">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.DeviceSimulator.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.dll</HintPath>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline.Editor">
        <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.Editor.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Assembly-CSharp-firstpass.csproj">
      <Project>{a0431b3a-6e27-fb34-197e-1acde9001a0f}</Project>
      <Name>Assembly-CSharp-firstpass</Name>
    </ProjectReference>
    <ProjectReference Include="Assembly-CSharp.csproj">
      <Project>{569a85a2-4aab-f951-60bb-1b6fc96e8c70}</Project>
      <Name>Assembly-CSharp</Name>
    </ProjectReference>
    <ProjectReference Include="Assembly-CSharp-Editor-firstpass.csproj">
      <Project>{71e01967-2b57-7768-b821-8a6b33ec3a6c}</Project>
      <Name>Assembly-CSharp-Editor-firstpass</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Machine.csproj">
      <Project>{c46542a4-cad3-0a16-0742-cec97b7020de}</Project>
      <Name>UniFramework.Machine</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Network.csproj">
      <Project>{32f18cff-ad0a-fac8-883d-ac8796c2bedc}</Project>
      <Name>UniFramework.Network</Name>
    </ProjectReference>
    <ProjectReference Include="YooAsset.Editor.csproj">
      <Project>{8d9f312e-1d9e-9a0a-b421-889026abeeca}</Project>
      <Name>YooAsset.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Singleton.csproj">
      <Project>{87944ebe-9e82-036b-18cb-a9f80c3fa62a}</Project>
      <Name>UniFramework.Singleton</Name>
    </ProjectReference>
    <ProjectReference Include="Proxima.Editor.csproj">
      <Project>{8ea8bc99-78ec-b440-8f0a-591fd1f43326}</Project>
      <Name>Proxima.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Event.csproj">
      <Project>{ef7c93d4-6571-47fa-6170-a3558a123916}</Project>
      <Name>UniFramework.Event</Name>
    </ProjectReference>
    <ProjectReference Include="spine-unity.csproj">
      <Project>{ce5d6b30-a2dd-b9db-65ae-bf6bf68ceff5}</Project>
      <Name>spine-unity</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Tween.csproj">
      <Project>{79a1243a-51ed-79d2-d0c6-d9fe5c9f779d}</Project>
      <Name>UniFramework.Tween</Name>
    </ProjectReference>
    <ProjectReference Include="GameRuntime.csproj">
      <Project>{384add0f-ab48-6bb7-0d2f-8a5f173dc79c}</Project>
      <Name>GameRuntime</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Animation.csproj">
      <Project>{108da027-93aa-7cb7-ac76-cecce677c424}</Project>
      <Name>UniFramework.Animation</Name>
    </ProjectReference>
    <ProjectReference Include="Proxima.csproj">
      <Project>{c0720ac4-aec5-5da5-a31e-a887f067c27e}</Project>
      <Name>Proxima</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnityResonanceEditor.csproj">
      <Project>{4d2d783a-89e5-af18-93e4-1cd1706fd8dd}</Project>
      <Name>FMODUnityResonanceEditor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Postprocessing.Editor.csproj">
      <Project>{258d3d40-386b-3526-f193-f03d28a8b899}</Project>
      <Name>Unity.Postprocessing.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Pooling.csproj">
      <Project>{fefe0f0d-5327-4908-feb7-148a7048665c}</Project>
      <Name>UniFramework.Pooling</Name>
    </ProjectReference>
    <ProjectReference Include="youhu.unity_uwa_sdk.Editor.csproj">
      <Project>{1829fc53-2793-8581-c2b2-ed4bcc7f8bf6}</Project>
      <Name>youhu.unity_uwa_sdk.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="RBG.Mulligan.csproj">
      <Project>{cbfc065b-8b45-689e-91b4-64cd9742c8b4}</Project>
      <Name>RBG.Mulligan</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Window.csproj">
      <Project>{f23b84e2-3bd6-49ef-de9b-ccea0def86fb}</Project>
      <Name>UniFramework.Window</Name>
    </ProjectReference>
    <ProjectReference Include="youhu.unity_uwa_sdk.csproj">
      <Project>{b53f86b4-8f62-c0d4-94f8-4b9e284011ad}</Project>
      <Name>youhu.unity_uwa_sdk</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Postprocessing.Runtime.csproj">
      <Project>{be55b292-cc44-17c8-5069-aa98556bdc44}</Project>
      <Name>Unity.Postprocessing.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="MeshEditor.Effects.RunTime.csproj">
      <Project>{a1e39cda-e28c-97a5-aff4-2a916ec95569}</Project>
      <Name>MeshEditor.Effects.RunTime</Name>
    </ProjectReference>
    <ProjectReference Include="NativeGallery.Editor.csproj">
      <Project>{58a638ce-8fe9-ad75-5f36-6492c5455e30}</Project>
      <Name>NativeGallery.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Utility.csproj">
      <Project>{7c28f787-7f16-1728-6d1f-db87d0ed1482}</Project>
      <Name>UniFramework.Utility</Name>
    </ProjectReference>
    <ProjectReference Include="Coffee.CFX_Demo_With_UIParticle.csproj">
      <Project>{a8b0ee9f-f9e9-f15d-63a5-a175e53896d7}</Project>
      <Name>Coffee.CFX_Demo_With_UIParticle</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnity.csproj">
      <Project>{b2a04fda-593f-2822-75b6-7a0ce85de5c8}</Project>
      <Name>FMODUnity</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnityEditor.csproj">
      <Project>{ac2f2cea-5f4d-2521-9100-a99822067e71}</Project>
      <Name>FMODUnityEditor</Name>
    </ProjectReference>
    <ProjectReference Include="NativeGallery.Runtime.csproj">
      <Project>{b25065d1-1177-4f4e-abc4-806c113ca925}</Project>
      <Name>NativeGallery.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnityResonance.csproj">
      <Project>{710aab89-2b5d-111c-d2b6-4b78886180d2}</Project>
      <Name>FMODUnityResonance</Name>
    </ProjectReference>
    <ProjectReference Include="spine-unity-editor.csproj">
      <Project>{c16d3fc2-d438-a49f-0d35-4d45f5039eb5}</Project>
      <Name>spine-unity-editor</Name>
    </ProjectReference>
    <ProjectReference Include="YooAsset.csproj">
      <Project>{8adfa466-57e6-7a4c-bea4-51b7aecf0597}</Project>
      <Name>YooAsset</Name>
    </ProjectReference>
    <ProjectReference Include="Coffee.UIParticle.csproj">
      <Project>{04540531-31b5-ecaf-3050-921f628688e1}</Project>
      <Name>Coffee.UIParticle</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
