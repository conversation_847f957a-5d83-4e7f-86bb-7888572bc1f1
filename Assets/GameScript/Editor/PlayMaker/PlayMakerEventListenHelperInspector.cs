using Fish.PlayMaker;
using UnityEditor;
using UnityEngine;

namespace Fish.PlayMakerEditor
{
	[CustomEditor(typeof(PlayMakerEventListenHelper))]
	public class PlayMakerEventListenHelperInspector : Editor
	{
		public override void OnInspectorGUI()
		{
			base.OnInspectorGUI();

			var helper = target as PlayMakerEventListenHelper;

			if (helper != null)
			{
				EditorGUILayout.Space();
				EditorGUILayout.LabelField("当前监听列表", EditorStyles.boldLabel);

				var infos = helper.EventListenDic;
				foreach (var kvp in infos)
				{
					var eventName = kvp.Key;
					var listenInfos = kvp.Value;

					EditorGUILayout.LabelField($"{eventName}: {listenInfos.Count} 个监听");
					EditorGUI.indentLevel++;
					foreach (var info in listenInfos)
					{
						EditorGUILayout.LabelField($"  -> {info.fsmName}.{info.fsmEventName}");
					}
					EditorGUI.indentLevel--;
				}
			}
		}
	}
}