using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace Fish
{
	[CustomEditor(typeof(DataStore))]
	public class DataStoreEditor : Editor
	{
		private string searchFilter = "";
		Dictionary<string, object> filteredDatas = new Dictionary<string, object>();

		public override void OnInspectorGUI()
		{
			base.OnInspectorGUI();

			var dataStore = (DataStore)target;
			var datas = dataStore.cache;

			EditorGUILayout.Space();

			EditorGUILayout.BeginHorizontal();
			EditorGUILayout.LabelField("搜索:", GUILayout.Width(60));
			searchFilter = EditorGUILayout.TextField(searchFilter);
			EditorGUILayout.EndHorizontal();

			Dictionary<string, object> displayDatas = null;

			if (string.IsNullOrWhiteSpace(searchFilter))
			{
				displayDatas = datas;
			}
			else
			{
				filteredDatas.Clear();
				foreach (var item in datas)
				{
					if (item.Key.ToLower().Contains(searchFilter.ToLower()))
					{
						if (filteredDatas == null)
						{
							filteredDatas = new Dictionary<string, object>();
						}
						filteredDatas.Add(item.Key, item.Value);
					}
				}
				displayDatas = filteredDatas;
			}

			EditorGUILayout.Space();

			foreach (var item in displayDatas)
			{
				EditorGUILayout.BeginVertical("box");

				// Key名称 - 使用粗体显示
				EditorGUILayout.LabelField("Key:", EditorStyles.boldLabel);
				EditorGUILayout.LabelField(item.Key, EditorStyles.textField, GUILayout.Height(EditorGUIUtility.singleLineHeight));

				// 值信息
				EditorGUILayout.LabelField("类型:", EditorStyles.boldLabel);
				if (item.Value == null)
				{
					EditorGUILayout.LabelField("null", EditorStyles.miniLabel);
				}
				else
				{
					var type = item.Value.GetType();
					EditorGUILayout.LabelField(type.Name, EditorStyles.miniLabel);

					EditorGUILayout.LabelField("值:", EditorStyles.boldLabel);
					EditorGUILayout.LabelField(item.Value.ToString(), EditorStyles.textField, GUILayout.Height(EditorGUIUtility.singleLineHeight));
				}

				EditorGUILayout.EndVertical();
				EditorGUILayout.Space();
			}
		}
	}
}