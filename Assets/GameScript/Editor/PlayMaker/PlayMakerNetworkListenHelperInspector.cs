using Fish.PlayMaker;
using UnityEditor;

namespace Fish.PlayMakerEditor
{
	[CustomEditor(typeof(PlayMakerNetworkListenHelper))]
	public class PlayMakerNetworkListenHelperInspector : Editor
	{
		public override void OnInspectorGUI()
		{
			base.OnInspectorGUI();

			var helper = target as PlayMakerNetworkListenHelper;

			if (helper != null)
			{
				EditorGUILayout.Space();
				EditorGUILayout.LabelField("当前监听列表", EditorStyles.boldLabel);

				var infos = helper.NetworkListenDic;
				foreach (var kvp in infos)
				{
					var cmdAction = kvp.Key;
					var listenInfos = kvp.Value;

					EditorGUILayout.LabelField($"{cmdAction}: {listenInfos.Count} 个监听");
					EditorGUI.indentLevel++;
					foreach (var info in listenInfos)
					{
						EditorGUILayout.LabelField($"  -> {info.fsmName}.{info.fsmEventName}");
					}
					EditorGUI.indentLevel--;
				}
			}
		}
	}
}