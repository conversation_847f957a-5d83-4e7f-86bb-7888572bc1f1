using System;
using System.Collections.Generic;
using UnityEngine;
using HutongGames.PlayMaker;

namespace Fish.PlayMaker
{
	public class PlayMakerEventListenHelper : MonoBehaviour
	{
		private Dictionary<string, List<EventListenInfo>> _eventListenDic = new Dictionary<string, List<EventListenInfo>>();
		public Dictionary<string, List<EventListenInfo>> EventListenDic => _eventListenDic;

		public struct EventListenInfo
		{
			public string fsmName;
			public string fsmEventName;
			public Action<object[]> callback;

			public EventListenInfo(string fsmName, string fsmEventName, Action<object[]> callback)
			{
				this.fsmName = fsmName;
				this.fsmEventName = fsmEventName;
				this.callback = callback;
			}
		}

		/// <summary>
		/// 监听事件并触发PlayMaker FSM事件
		/// </summary>
		/// <param name="eventName">要监听的事件名</param>
		/// <param name="fsmName">目标FSM名称</param>
		/// <param name="fsmEventName">要触发的FSM事件名</param>
		public void Listen(string eventName, string fsmName, string fsmEventName)
		{
			if (string.IsNullOrEmpty(eventName) || string.IsNullOrEmpty(fsmName) || string.IsNullOrEmpty(fsmEventName))
			{
				GameLogger.LogError("PlayMakerEventListenHelper: eventName, fsmName, fsmEventName 都不能为空");
				return;
			}

			// 检查是否已经存在相同的监听
			if (_eventListenDic.TryGetValue(eventName, out var existingListens))
			{
				foreach (var info in existingListens)
				{
					if (info.fsmName == fsmName && info.fsmEventName == fsmEventName)
					{
						GameLogger.LogWarning($"PlayMakerEventListenHelper: 已存在监听 {eventName} -> {fsmName}.{fsmEventName}");
						return;
					}
				}
			}

			void EventCallback(object[] args)
			{
				TriggerPlayMakerEvent(fsmName, fsmEventName, args);
			}
			Action<object[]> callback = EventCallback;

			// 添加到EventCenter监听
			EventCenter.Listen(eventName, callback);

			// 记录监听信息
			if (!_eventListenDic.ContainsKey(eventName))
			{
				_eventListenDic[eventName] = new List<EventListenInfo>();
			}

			var listenInfo = new EventListenInfo(fsmName, fsmEventName, callback);
			_eventListenDic[eventName].Add(listenInfo);
		}

		/// <summary>
		/// 移除指定的事件监听
		/// </summary>
		/// <param name="eventName">事件名</param>
		/// <param name="fsmName">FSM名称</param>
		/// <param name="fsmEventName">FSM事件名</param>
		public void RemoveListen(string eventName, string fsmName, string fsmEventName)
		{
			if (!_eventListenDic.ContainsKey(eventName))
				return;

			var listenInfos = _eventListenDic[eventName];

			for (int i = listenInfos.Count - 1; i >= 0; i--)
			{
				var info = listenInfos[i];
				if (info.fsmName == fsmName && info.fsmEventName == fsmEventName)
				{
					EventCenter.Ignore(eventName, info.callback);
					listenInfos.RemoveAt(i);
					break;
				}
			}

			// 如果该事件没有监听了，清理字典
			if (listenInfos.Count == 0)
			{
				_eventListenDic.Remove(eventName);
			}
		}

		/// <summary>
		/// 移除指定事件的所有监听
		/// </summary>
		/// <param name="eventName">事件名</param>
		public void RemoveAllListenForEvent(string eventName)
		{
			if (!_eventListenDic.ContainsKey(eventName))
				return;

			var listenInfos = _eventListenDic[eventName];

			// 从EventCenter取消所有监听
			foreach (var info in listenInfos)
			{
				EventCenter.Ignore(eventName, info.callback);
			}

			_eventListenDic.Remove(eventName);
		}

		/// <summary>
		/// 触发PlayMaker FSM事件
		/// </summary>
		/// <param name="fsmName">FSM名称</param>
		/// <param name="fsmEventName">FSM事件名</param>
		/// <param name="args">事件参数</param>
		private void TriggerPlayMakerEvent(string fsmName, string fsmEventName, object[] args)
		{
			try
			{
				PlayMakerFSM targetFsm = FindFSMByName(fsmName);

				if (targetFsm != null)
				{
					gameObject.SetDataObject($"{fsmName}.{fsmEventName}.eventData", args);
					targetFsm.SendEvent(fsmEventName);
				}
				else
				{
					GameLogger.LogWarning($"PlayMakerEventListenHelper: 未找到名为 '{fsmName}' 的FSM组件");
				}
			}
			catch (Exception e)
			{
				GameLogger.LogError($"PlayMakerEventListenHelper: 触发FSM事件时发生错误: {e.Message}");
			}
		}

		/// <summary>
		/// 根据名称查找FSM组件
		/// </summary>
		/// <param name="fsmName">FSM名称</param>
		/// <returns>找到的FSM组件，如果没找到返回null</returns>
		private PlayMakerFSM FindFSMByName(string fsmName)
		{
			PlayMakerFSM[] fsmComponents = GetComponents<PlayMakerFSM>();
			foreach (var fsm in fsmComponents)
			{
				if (fsm.FsmName == fsmName)
				{
					return fsm;
				}
			}

			return null;
		}

		void OnDisable()
		{
			// 取消所有事件监听
			foreach (var kvp in _eventListenDic)
			{
				string eventName = kvp.Key;
				var listenInfos = kvp.Value;

				foreach (var info in listenInfos)
				{
					EventCenter.Ignore(eventName, info.callback);
				}
			}

			// 清理所有记录
			_eventListenDic.Clear();
		}
	}
}