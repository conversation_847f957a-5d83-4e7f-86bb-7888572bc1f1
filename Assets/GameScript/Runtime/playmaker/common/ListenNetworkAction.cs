using HutongGames.PlayMaker;

namespace Fish.PlayMaker
{
	[ActionCategory("Fish.Common")]
	public class ListenNetworkAction : FsmStateAction
	{
		[Tooltip("接收回调的GameObject")]
		public FsmOwnerDefault TargetGo;

		[RequiredField]
		[HutongGames.PlayMaker.Tooltip("事件名")]
		public FsmString eventName;

		[RequiredField]
		[HutongGames.PlayMaker.Tooltip("回调的FSM名称")]
		public FsmString fsmName;
		
		[RequiredField]
		[HutongGames.PlayMaker.Tooltip("回调的FSM事件名")]
		public FsmString fsmEventName;

		public override void OnEnter()
		{
			var targetGo = Fsm.GetOwnerDefaultTarget(TargetGo);
			var helper = targetGo.GetComponent<PlayMakerNetworkListenHelper>();
			if (helper == null)
			{
				helper = targetGo.AddComponent<PlayMakerNetworkListenHelper>();
			}

			helper.ListenNetworkMessage(eventName.Value, fsmName.Value, fsmEventName.Value);

			Finish();
		}
	}

}