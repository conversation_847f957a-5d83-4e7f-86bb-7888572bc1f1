using System;
using System.Collections.Generic;
using UnityEngine;
using HutongGames.PlayMaker;

namespace Fish.PlayMaker
{
	public class PlayMakerNetworkListenHelper : MonoBehaviour
	{
		private Dictionary<string, List<NetworkListenInfo>> _networkListenDic = new Dictionary<string, List<NetworkListenInfo>>();
		public Dictionary<string, List<NetworkListenInfo>> NetworkListenDic => _networkListenDic;

		public struct NetworkListenInfo
		{
			public string fsmName;
			public string fsmEventName;
			public Action<object> callback;

			public NetworkListenInfo(string fsmName, string fsmEventName, Action<object> callback)
			{
				this.fsmName = fsmName;
				this.fsmEventName = fsmEventName;
				this.callback = callback;
			}
		}

		/// <summary>
		/// 监听网络消息并触发PlayMaker FSM事件
		/// </summary>
		/// <param name="cmdAction">要监听的网络消息命令（cmd+action格式）</param>
		/// <param name="fsmName">目标FSM名称</param>
		/// <param name="fsmEventName">要触发的FSM事件名</param>
		public void ListenNetworkMessage(string cmdAction, string fsmName, string fsmEventName)
		{
			if (string.IsNullOrEmpty(cmdAction) || string.IsNullOrEmpty(fsmName) || string.IsNullOrEmpty(fsmEventName))
			{
				GameLogger.LogError("PlayMakerNetworkListenHelper: cmdAction, fsmName, fsmEventName 都不能为空");
				return;
			}

			// 检查是否已经存在相同的监听
			if (_networkListenDic.TryGetValue(cmdAction, out var existingListens))
			{
				foreach (var info in existingListens)
				{
					if (info.fsmName == fsmName && info.fsmEventName == fsmEventName)
					{
						GameLogger.LogWarning($"PlayMakerNetworkListenHelper: 已存在网络消息监听 {cmdAction} -> {fsmName}.{fsmEventName}");
						return;
					}
				}
			}

			void NetworkCallback(object data)
			{
				TriggerPlayMakerNetworkEvent(fsmName, fsmEventName, cmdAction, data);
			}
			Action<object> callback = NetworkCallback;

			// 添加到NetCenter监听
			NetCenter.Listen(cmdAction, callback);

			// 记录监听信息
			if (!_networkListenDic.ContainsKey(cmdAction))
			{
				_networkListenDic[cmdAction] = new List<NetworkListenInfo>();
			}

			var listenInfo = new NetworkListenInfo(fsmName, fsmEventName, callback);
			_networkListenDic[cmdAction].Add(listenInfo);
		}

		/// <summary>
		/// 移除指定的网络消息监听
		/// </summary>
		/// <param name="cmdAction">网络消息命令</param>
		/// <param name="fsmName">FSM名称</param>
		/// <param name="fsmEventName">FSM事件名</param>
		public void RemoveNetworkListen(string cmdAction, string fsmName, string fsmEventName)
		{
			if (!_networkListenDic.ContainsKey(cmdAction))
				return;

			var listenInfos = _networkListenDic[cmdAction];

			for (int i = listenInfos.Count - 1; i >= 0; i--)
			{
				var info = listenInfos[i];
				if (info.fsmName == fsmName && info.fsmEventName == fsmEventName)
				{
					NetCenter.Ignore(cmdAction, info.callback);
					listenInfos.RemoveAt(i);
					break;
				}
			}

			// 如果该网络消息没有监听了，清理字典
			if (listenInfos.Count == 0)
			{
				_networkListenDic.Remove(cmdAction);
			}
		}

		/// <summary>
		/// 移除指定网络消息的所有监听
		/// </summary>
		/// <param name="cmdAction">网络消息命令</param>
		public void RemoveAllListenForNetworkMessage(string cmdAction)
		{
			if (!_networkListenDic.ContainsKey(cmdAction))
				return;

			var listenInfos = _networkListenDic[cmdAction];

			// 从NetCenter取消所有监听
			foreach (var info in listenInfos)
			{
				NetCenter.Ignore(cmdAction, info.callback);
			}

			_networkListenDic.Remove(cmdAction);
		}

		/// <summary>
		/// 触发PlayMaker FSM网络事件
		/// </summary>
		/// <param name="fsmName">FSM名称</param>
		/// <param name="fsmEventName">FSM事件名</param>
		/// <param name="cmdAction">网络消息命令</param>
		/// <param name="data">网络消息数据</param>
		private void TriggerPlayMakerNetworkEvent(string fsmName, string fsmEventName, string cmdAction, object data)
		{
			try
			{
				PlayMakerFSM targetFsm = FindFSMByName(fsmName);

				if (targetFsm != null)
				{
					string dataKey = $"{fsmName}.{fsmEventName}.networkData";
					gameObject.SetDataObject(dataKey, data);

					// 触发FSM事件
					targetFsm.SendEvent(fsmEventName);
				}
				else
				{
					GameLogger.LogWarning($"PlayMakerNetworkListenHelper: 未找到名为 '{fsmName}' 的FSM组件");
				}
			}
			catch (Exception e)
			{
				GameLogger.LogError($"PlayMakerNetworkListenHelper: 触发FSM网络事件时发生错误: {e.Message}");
			}
		}

		/// <summary>
		/// 根据名称查找FSM组件
		/// </summary>
		/// <param name="fsmName">FSM名称</param>
		/// <returns>找到的FSM组件，如果没找到返回null</returns>
		private PlayMakerFSM FindFSMByName(string fsmName)
		{
			PlayMakerFSM[] fsmComponents = GetComponents<PlayMakerFSM>();
			foreach (var fsm in fsmComponents)
			{
				if (fsm.FsmName == fsmName)
				{
					return fsm;
				}
			}

			return null;
		}

		void OnDisable()
		{
			// 取消所有网络消息监听
			foreach (var kvp in _networkListenDic)
			{
				string cmdAction = kvp.Key;
				var listenInfos = kvp.Value;

				foreach (var info in listenInfos)
				{
					NetCenter.Ignore(cmdAction, info.callback);
				}
			}

			// 清理所有记录
			_networkListenDic.Clear();
		}
	}
}
